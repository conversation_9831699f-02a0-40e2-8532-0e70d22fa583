<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>飞书多维表格 API 交互式指南</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Visualization & Content Choices:
        - Core Concepts: HTML table & HTML/CSS conceptual diagram (App -> Table -> Record -> Field) for clarity. Goal: Organize/Inform. No JS lib.
        - API Operations: Structured text, code blocks (cURL, JSON). Goal: Inform/Demonstrate. Interaction: Copy buttons.
            - ✨ AI Record Generation (in Add Records): Text input + Button -> Gemini API -> Populates example request. Goal: Assist/Automate.
            - ✨ AI Filter Generation (in Search Records): Text input + Button -> Gemini API -> Populates example filter. Goal: Assist/Simplify.
        - SDK Python Example: Syntax-highlighted code block. Goal: Demonstrate. Interaction: Copy button.
        - API Rate Limits: Bar chart (Chart.js/Canvas) comparing limits for different endpoints. Goal: Compare/Inform. Interaction: Tooltips.
        - Important Notes: Accordion/Expandable sections (HTML/CSS/JS). Goal: Inform.
        - CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }
        .content-section {
            display: none;
        }
        .content-section.active {
            display: block;
        }
        .code-block {
            background-color: #f3f4f6; /* bg-gray-100 */
            border-radius: 0.375rem; /* rounded-md */
            padding: 1rem; /* p-4 */
            overflow-x: auto;
            position: relative;
        }
        .code-block pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            color: #1f2937; /* text-gray-800 */
        }
        .copy-btn {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background-color: #4b5563; /* bg-gray-600 */
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem; /* rounded-sm */
            font-size: 0.75rem; /* text-xs */
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .copy-btn:hover {
            background-color: #374151; /* bg-gray-700 */
        }
        .nav-link {
            transition: all 0.3s ease;
        }
        .nav-link.active {
            background-color: #2563eb; /* bg-blue-600 */
            color: white;
            font-weight: 600;
        }
        .nav-link:not(.active):hover {
            background-color: #eff6ff; /* bg-blue-50 */
            color: #1d4ed8; /* text-blue-700 */
        }
        .accordion-header {
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .accordion-header:hover {
            background-color: #f0f4f8;
        }
        .accordion-content {
            display: none;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
         .accordion-content.open {
            display: block;
            max-height: 1000px; /* Adjust as needed */
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 700px; /* Adjusted max-width */
            margin-left: auto;
            margin-right: auto;
            height: 350px; /* Base height */
            max-height: 400px; /* Max height */
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 400px;
            }
        }
        .concept-diagram {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem; /* space-y-2 */
            padding: 1rem; /* p-4 */
            border: 1px solid #e5e7eb; /* border-gray-200 */
            border-radius: 0.375rem; /* rounded-md */
            background-color: #f9fafb; /* bg-gray-50 */
        }
        .concept-item {
            background-color: #dbeafe; /* bg-blue-100 */
            color: #1e40af; /* text-blue-800 */
            padding: 0.5rem 1rem; /* py-2 px-4 */
            border-radius: 0.375rem; /* rounded-md */
            font-weight: 500; /* font-medium */
            text-align: center;
            min-width: 150px;
        }
        .concept-arrow {
            font-size: 1.5rem; /* text-2xl */
            color: #6b7280; /* text-gray-500 */
        }
        .gemini-feature-input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #d1d5db; /* border-gray-300 */
            border-radius: 0.375rem; /* rounded-md */
            margin-bottom: 0.5rem;
        }
        .gemini-feature-btn {
            background-color: #10b981; /* bg-emerald-500 */
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem; /* rounded-md */
            font-weight: 500; /* font-medium */
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .gemini-feature-btn:hover {
            background-color: #059669; /* bg-emerald-600 */
        }
        .gemini-status {
            font-size: 0.875rem; /* text-sm */
            margin-top: 0.5rem;
            min-height: 1.25rem; /* For consistent spacing */
        }
    </style>
</head>
<body class="bg-slate-50 text-slate-800 antialiased">

    <header class="bg-white shadow-md sticky top-0 z-50">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center">
                    <svg class="h-8 w-auto text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h1 class="ml-3 text-2xl font-bold text-slate-900">飞书多维表格 API 指南</h1>
                </div>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8 flex flex-col md:flex-row gap-8">
        <nav class="md:w-1/4 lg:w-1/5 space-y-2">
            <a href="#" data-target="home" class="nav-link block px-4 py-2 rounded-md text-slate-700">🚀 首页</a>
            <a href="#" data-target="prerequisites" class="nav-link block px-4 py-2 rounded-md text-slate-700">🛠️ 准备工作</a>
            <a href="#" data-target="get-token" class="nav-link block px-4 py-2 rounded-md text-slate-700">🔑 获取访问令牌</a>
            <div>
                <span class="block px-4 py-2 text-sm font-semibold text-slate-500 uppercase tracking-wider">核心 API 操作</span>
                <a href="#" data-target="api-list-tables" class="nav-link block pl-8 pr-4 py-2 rounded-md text-slate-600 text-sm">列出数据表</a>
                <a href="#" data-target="api-search-records" class="nav-link block pl-8 pr-4 py-2 rounded-md text-slate-600 text-sm">搜索/读取记录</a>
                <a href="#" data-target="api-add-records" class="nav-link block pl-8 pr-4 py-2 rounded-md text-slate-600 text-sm">添加记录</a>
                <a href="#" data-target="api-update-records" class="nav-link block pl-8 pr-4 py-2 rounded-md text-slate-600 text-sm">更新记录</a>
                <a href="#" data-target="api-delete-records" class="nav-link block pl-8 pr-4 py-2 rounded-md text-slate-600 text-sm">删除记录</a>
            </div>
            <a href="#" data-target="sdk-usage" class="nav-link block px-4 py-2 rounded-md text-slate-700">📦 SDK 使用</a>
            <a href="#" data-target="important-notes" class="nav-link block px-4 py-2 rounded-md text-slate-700">⚠️ 重要注意事项</a>
            <a href="#" data-target="resources" class="nav-link block px-4 py-2 rounded-md text-slate-700">📚 资源链接</a>
        </nav>

        <main class="md:w-3/4 lg:w-4/5 bg-white p-6 sm:p-8 rounded-lg shadow-lg">
            <section id="home" class="content-section space-y-6">
                <h2 class="text-3xl font-semibold text-slate-900 border-b pb-3">欢迎使用飞书多维表格 API 交互式指南</h2>
                <p class="text-lg text-slate-700">
                    飞书多维表格 (Feishu Bitable) 是一款强大的数据管理与协作工具。通过其开放 API，开发者可以将多维表格的数据与外部系统集成，实现数据同步、自动化流程、自定义数据分析与展示等高级功能。
                </p>
                <p class="text-slate-600">
                    本指南旨在帮助您快速理解和上手使用飞书多维表格 API。您可以通过左侧导航栏探索各个主题，从准备工作到核心 API 操作，再到 SDK 的使用和重要注意事项。部分核心操作集成了 ✨ <strong class="text-emerald-600">Gemini AI 智能辅助功能</strong>，帮助您更轻松地生成请求示例。
                </p>
                <p class="text-slate-600">
                    让我们开始探索飞书多维表格 API 的强大功能吧！
                </p>
            </section>

            <section id="prerequisites" class="content-section space-y-6">
                <h2 class="text-3xl font-semibold text-slate-900 border-b pb-3">准备工作：API 访问的前提条件</h2>
                <p class="text-slate-700">在开始通过 API 与飞书多维表格交互之前，必须完成一系列准备工作，包括创建飞书应用、获取必要的凭证以及理解核心概念。这部分内容将引导您完成这些基础步骤，为后续的 API 调用打下坚实的基础。</p>

                <div class="space-y-4">
                    <h3 class="text-2xl font-medium text-slate-800">A. 创建企业自建应用并获取凭证</h3>
                    <ol class="list-decimal list-inside space-y-2 text-slate-600 pl-4">
                        <li><strong>登录飞书开放平台并创建应用</strong>: 访问 <a href="https://open.feishu.cn/app" target="_blank" class="text-blue-600 hover:underline">飞书开放平台</a>，点击“创建企业自建应用”，填写应用基本信息。</li>
                        <li><strong>为应用配置 API 权限</strong>: 在应用后台的“权限管理”中，申请访问多维表格所需的权限，如 `bitable:app:readonly`, `bitable:app` 等。</li>
                        <li><strong>创建并发布应用版本</strong>: 申请权限后，创建新版本并申请线上发布，使 API 权限生效。</li>
                        <li><strong>获取应用凭证</strong>: 在应用的“凭证与基础信息”页面，复制并妥善保管 `App ID` 和 `App Secret`。</li>
                    </ol>
                </div>

                <div class="space-y-4">
                    <h3 class="text-2xl font-medium text-slate-800">B. 理解核心概念</h3>
                    <p class="text-slate-600">飞书多维表格 API 围绕一些核心资源和标识符进行操作。下图展示了它们之间的层级关系：</p>
                    <div class="concept-diagram my-4">
                        <div class="concept-item">多维表格应用 (Base/App)</div>
                        <div class="concept-arrow">⬇️</div>
                        <div class="concept-item">数据表 (Table)</div>
                        <div class="concept-arrow">⬇️</div>
                        <div class="concept-item">视图 (View)</div>
                        <div class="concept-arrow">⬇️</div>
                        <div class="concept-item">记录 (Record)</div>
                        <div class="concept-arrow">⬇️</div>
                        <div class="concept-item">字段 (Field)</div>
                    </div>
                    <p class="text-slate-600">下表总结了这些核心概念及其标识符：</p>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-md">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">概念</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标识符</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">描述</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">如何获取</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200 text-sm text-slate-600">
                                <tr>
                                    <td class="px-4 py-3">多维表格应用 (Base/App)</td>
                                    <td class="px-4 py-3 font-mono">app_token</td>
                                    <td class="px-4 py-3">多维表格文档的唯一标识。</td>
                                    <td class="px-4 py-3">从 URL 中解析；特定 API 获取。</td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3">数据表 (Table)</td>
                                    <td class="px-4 py-3 font-mono">table_id</td>
                                    <td class="px-4 py-3">在一个 `app_token` 内，数据表的唯一标识。</td>
                                    <td class="px-4 py-3">从 URL 中解析；调用“列出数据表” API。</td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3">视图 (View)</td>
                                    <td class="px-4 py-3 font-mono">view_id</td>
                                    <td class="px-4 py-3">在一个 `app_token` 内，视图的唯一标识。</td>
                                    <td class="px-4 py-3">从 URL 中解析；调用“列出视图” API。</td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3">记录 (Record)</td>
                                    <td class="px-4 py-3 font-mono">record_id</td>
                                    <td class="px-4 py-3">在一个 `app_token` 内，数据行的唯一标识。</td>
                                    <td class="px-4 py-3">调用“搜索记录” API；创建记录后的响应。</td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3">字段 (Field)</td>
                                    <td class="px-4 py-3 font-mono">字段名 / API 字段名</td>
                                    <td class="px-4 py-3">数据列的名称。API 调用时通常使用 API 字段名。</td>
                                    <td class="px-4 py-3">创建字段时设定；调用“列出字段” API。</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="space-y-4">
                    <h3 class="text-2xl font-medium text-slate-800">C. API 访问凭证 (Access Token)</h3>
                    <p class="text-slate-600">飞书 API 调用需要 Access Token 进行身份验证。主要有以下几种：</p>
                    <ul class="list-disc list-inside space-y-2 text-slate-600 pl-4">
                        <li><strong>`tenant_access_token` (应用维度凭证)</strong>: 代表应用自身访问企业资源，无需用户授权。适用于后台服务、自动化脚本。本指南后续示例主要使用此凭证。</li>
                        <li><strong>`user_access_token` (用户维度凭证)</strong>: 代表应用以某个用户身份操作，需用户 OAuth 授权。</li>
                        <li><strong>`app_access_token` (应用身份凭证)</strong>: 应用身份的短期凭证，用于获取 `tenant_access_token` (商店应用场景)等。</li>
                    </ul>
                    <p class="text-slate-600 font-semibold">重要提示: 确保应用不仅拥有 API Scope 权限，还被添加为目标多维表格的协作者，并具有足够的读写权限。如果多维表格开启了“高级权限”，需在高级权限设置中为应用配置相应权限。</p>
                </div>
            </section>

            <section id="get-token" class="content-section space-y-6">
                <h2 class="text-3xl font-semibold text-slate-900 border-b pb-3">获取访问凭证 (`tenant_access_token`)</h2>
                <p class="text-slate-700">对于企业自建应用，访问多维表格数据最常用的凭证是 `tenant_access_token`。它允许应用以自己的身份进行操作，非常适合服务器端的自动化任务。获取后应缓存并在过期前刷新。</p>

                <div class="space-y-2">
                    <h4 class="text-xl font-medium text-slate-800">API 端点与请求方法</h4>
                    <p class="text-slate-600"><strong>API 端点</strong>: <code class="bg-gray-100 px-1 rounded">POST /open-apis/auth/v3/tenant_access_token/internal/</code></p>
                    <p class="text-slate-600"><strong>HTTP 方法</strong>: <code class="bg-gray-100 px-1 rounded">POST</code></p>
                    <p class="text-slate-600"><strong>Content-Type</strong>: <code class="bg-gray-100 px-1 rounded">application/json; charset=utf-8</code></p>
                </div>

                <div class="space-y-2">
                    <h4 class="text-xl font-medium text-slate-800">请求体参数</h4>
                    <ul class="list-disc list-inside text-slate-600 pl-4">
                        <li>`app_id` (string, 必需): 应用的 App ID。</li>
                        <li>`app_secret` (string, 必需): 应用的 App Secret。</li>
                    </ul>
                </div>

                <div class="space-y-2">
                    <h4 class="text-xl font-medium text-slate-800">cURL 请求示例</h4>
                    <div class="code-block">
                        <button class="copy-btn" onclick="copyCode(this)">复制</button>
                        <pre><code>curl -X POST \
  'https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/' \
  -H 'Content-Type: application/json; charset=utf-8' \
  -d '{
        "app_id": "YOUR_APP_ID",
        "app_secret": "YOUR_APP_SECRET"
      }'</code></pre>
                    </div>
                </div>

                <div class="space-y-2">
                    <h4 class="text-xl font-medium text-slate-800">响应体结构</h4>
                    <div class="code-block">
                        <button class="copy-btn" onclick="copyCode(this)">复制</button>
                        <pre><code>{
    "code": 0,
    "msg": "success",
    "tenant_access_token": "t-xxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    "expire": 7200
}</code></pre>
                    </div>
                    <ul class="list-disc list-inside text-slate-600 pl-4">
                        <li>`code`: 错误码，0 表示成功。</li>
                        <li>`msg`: 错误描述。</li>
                        <li>`tenant_access_token`: 获取到的应用维度访问凭证。</li>
                        <li>`expire`: `tenant_access_token` 的有效时间，单位秒 (通常 7200 秒，即 2 小时)。</li>
                    </ul>
                </div>
            </section>

            <section id="api-list-tables" class="content-section space-y-6">
                <h2 class="text-3xl font-semibold text-slate-900 border-b pb-3">列出数据表</h2>
                <p class="text-slate-700">此 API 用于获取指定 `app_token` 下的所有数据表信息。所有 API 请求都需要在 HTTP Header 中加入 `Authorization` 字段，其值为 `Bearer YOUR_TENANT_ACCESS_TOKEN`。</p>

                <div class="space-y-2">
                    <h4 class="text-xl font-medium text-slate-800">API 端点与方法</h4>
                    <p class="text-slate-600"><strong>端点</strong>: <code class="bg-gray-100 px-1 rounded">GET /open-apis/bitable/v1/apps/:app_token/tables</code></p>
                    <p class="text-slate-600"><strong>路径参数</strong>: `:app_token` (多维表格应用的唯一标识)</p>
                    <p class="text-slate-600"><strong>查询参数 (可选)</strong>: `page_size`, `page_token`</p>
                    <p class="text-slate-600"><strong>所需权限 Scope</strong>: `bitable:app:readonly` 或 `bitable:app`</p>
                </div>

                <div class="space-y-2">
                    <h4 class="text-xl font-medium text-slate-800">cURL 示例</h4>
                    <div class="code-block">
                        <button class="copy-btn" onclick="copyCode(this)">复制</button>
                        <pre><code>curl -X GET \
  'https://open.feishu.cn/open-apis/bitable/v1/apps/YOUR_APP_TOKEN/tables?page_size=10' \
  -H 'Authorization: Bearer YOUR_TENANT_ACCESS_TOKEN' \
  -H 'Content-Type: application/json; charset=utf-8'</code></pre>
                    </div>
                </div>

                <div class="space-y-2">
                    <h4 class="text-xl font-medium text-slate-800">响应解析</h4>
                    <div class="code-block">
                        <button class="copy-btn" onclick="copyCode(this)">复制</button>
                        <pre><code>{
    "code": 0,
    "msg": "success",
    "data": {
        "has_more": false,
        "page_token": "",
        "total": 1,
        "items": [
            {
                "table_id": "tblxxxxxx",
                "revision": 1,
                "name": "默认数据表"
            }
        ]
    }
}</code></pre>
                    </div>
                    <p class="text-slate-600">响应体包含 `items` (数据表列表)、`total` (总表数) 以及用于分页的 `has_more` 和 `page_token`。</p>
                </div>
            </section>

            <section id="api-search-records" class="content-section space-y-6">
                <h2 class="text-3xl font-semibold text-slate-900 border-b pb-3">搜索和读取记录</h2>
                <p class="text-slate-700">读取记录是与多维表格 API 交互最常见的操作之一。主要通过“搜索记录”接口实现，支持筛选、排序和分页。</p>

                <div class="space-y-4">
                    <h3 class="text-2xl font-medium text-slate-800">按条件搜索记录</h3>
                    <div class="space-y-2">
                        <h4 class="text-xl font-medium text-slate-800">API 端点与方法</h4>
                        <p class="text-slate-600"><strong>端点</strong>: <code class="bg-gray-100 px-1 rounded">POST /open-apis/bitable/v1/apps/:app_token/tables/:table_id/records/search</code></p>
                        <p class="text-slate-600"><strong>路径参数</strong>: `:app_token`, `:table_id`</p>
                        <p class="text-slate-600"><strong>请求体参数 (JSON)</strong>: `view_id` (可选), `field_names` (可选), `filter` (可选, 定义筛选条件), `sort` (可选), `page_size` (可选, 最大500), `page_token` (可选), `user_id_type` (可选), `automatic_fields` (可选)。</p>
                        <p class="text-slate-600"><strong>所需权限 Scope</strong>: `bitable:app:readonly` 或 `bitable:app`</p>
                    </div>

                    <div class="mt-6 p-4 border border-emerald-200 rounded-lg bg-emerald-50 space-y-3">
                        <h4 class="text-lg font-medium text-emerald-700">✨ AI 智能生成筛选条件</h4>
                        <p class="text-sm text-emerald-600">用自然语言描述您想筛选的条件，AI 将尝试生成 JSON 格式的 `filter` 对象。</p>
                        <input type="text" id="aiFilterQueryInput" class="gemini-feature-input" placeholder="例如：查找所有本周五之前到期的高优先级任务">
                        <button id="generateFilterBtn" class="gemini-feature-btn">✨ AI 生成筛选条件</button>
                        <p id="aiFilterStatus" class="gemini-status text-emerald-600"></p>
                    </div>

                    <div class="space-y-2 mt-4">
                        <h4 class="text-xl font-medium text-slate-800">cURL 示例 (搜索状态为 "Open" 的任务)</h4>
                        <div id="searchRecordsCurlExample" class="code-block">
                            <button class="copy-btn" onclick="copyCode(this)">复制</button>
                            <pre><code>curl -X POST \
  'https://open.feishu.cn/open-apis/bitable/v1/apps/YOUR_APP_TOKEN/tables/YOUR_TABLE_ID/records/search' \
  -H 'Authorization: Bearer YOUR_TENANT_ACCESS_TOKEN' \
  -H 'Content-Type: application/json; charset=utf-8' \
  -d '{
        "filter": {
            "conjunction": "and",
            "conditions": [
                {
                    "field_name": "状态字段API名", 
                    "operator": "is",
                    "value": ["Open"]
                }
            ]
        },
        "page_size": 10,
        "field_names": ["任务标题API名", "负责人API名", "状态字段API名"] 
      }'</code></pre>
                        </div>
                         <p class="text-sm text-slate-500 mt-1">注意: `filter` 对象的结构相对复杂，请参考官方文档中关于“记录筛选开发指南”的部分。字段名需使用 API 名称。您可以使用上方的 AI 功能辅助生成。</p>
                    </div>

                    <div class="space-y-2">
                        <h4 class="text-xl font-medium text-slate-800">响应解析</h4>
                        <p class="text-slate-600">响应体 `data.items` 包含记录对象列表，每个对象有 `record_id` 和 `fields`。`data.total` 是满足条件的总记录数。</p>
                    </div>
                </div>

                <div class="space-y-4 mt-6 border-t pt-6">
                    <h3 class="text-2xl font-medium text-slate-800">根据记录 ID 批量获取</h3>
                     <p class="text-slate-600">当已知 `record_id` 时，此接口更高效，一次最多可查询 100 条记录。</p>
                    <div class="space-y-2">
                        <h4 class="text-xl font-medium text-slate-800">API 端点与方法</h4>
                        <p class="text-slate-600"><strong>端点</strong>: <code class="bg-gray-100 px-1 rounded">POST /open-apis/bitable/v1/apps/:app_token/tables/:table_id/records/batch_get</code></p>
                        <p class="text-slate-600"><strong>请求体</strong>: `{"record_ids": ["recxxxx1", "recxxxx2"], "user_id_type": "open_id"}`</p>
                    </div>
                </div>
            </section>

            <section id="api-add-records" class="content-section space-y-6">
                <h2 class="text-3xl font-semibold text-slate-900 border-b pb-3">添加新记录</h2>
                <p class="text-slate-700">向数据表中添加新的记录。字段键必须是字段的 API 名称。</p>

                <div class="space-y-2">
                    <h4 class="text-xl font-medium text-slate-800">API 端点与方法</h4>
                    <p class="text-slate-600"><strong>端点</strong>: <code class="bg-gray-100 px-1 rounded">POST /open-apis/bitable/v1/apps/:app_token/tables/:table_id/records</code></p>
                    <p class="text-slate-600"><strong>路径参数</strong>: `:app_token`, `:table_id`</p>
                    <p class="text-slate-600"><strong>请求体 (JSON)</strong>: 包含 `fields` 对象，其键为字段 API 名称，值为字段内容。不同字段类型数据格式不同 (如人员字段、附件字段)。</p>
                    <p class="text-slate-600"><strong>所需权限 Scope</strong>: `bitable:app`</p>
                </div>
                
                <div class="mt-6 p-4 border border-emerald-200 rounded-lg bg-emerald-50 space-y-3">
                    <h4 class="text-lg font-medium text-emerald-700">✨ AI 智能生成记录内容</h4>
                    <p class="text-sm text-emerald-600">简要描述您想创建的记录，AI 将尝试生成 JSON 格式的 `fields` 对象。</p>
                    <input type="text" id="aiRecordDescriptionInput" class="gemini-feature-input" placeholder="例如：一个关于修复登录页面bug的任务">
                    <button id="generateRecordBtn" class="gemini-feature-btn">✨ AI 生成记录内容</button>
                    <p id="aiRecordStatus" class="gemini-status text-emerald-600"></p>
                </div>

                 <div class="space-y-2 mt-4">
                    <h4 class="text-xl font-medium text-slate-800">请求体示例</h4>
                    <div id="addRecordRequestExample" class="code-block">
                        <button class="copy-btn" onclick="copyCode(this)">复制</button>
                        <pre><code>{
    "fields": {
        "任务标题API名": "API 创建的新任务",
        "优先级API名": "高",
        "负责人API名": [{"id": "ou_xxxxxxxx"}] 
    }
}</code></pre>
                    </div>
                     <p class="text-sm text-slate-500 mt-1">注意: 人员字段通常需要一个包含用户 ID (根据 `user_id_type` 指定的类型) 的对象数组。附件字段需先上传文件获取 `file_token`。您可以使用上方的 AI 功能辅助生成 `fields` 内容。</p>
                </div>

                <div class="space-y-2">
                    <h4 class="text-xl font-medium text-slate-800">cURL 示例</h4>
                    <div class="code-block">
                        <button class="copy-btn" onclick="copyCode(this)">复制</button>
                        <pre><code>curl -X POST \
  'https://open.feishu.cn/open-apis/bitable/v1/apps/YOUR_APP_TOKEN/tables/YOUR_TABLE_ID/records' \
  -H 'Authorization: Bearer YOUR_TENANT_ACCESS_TOKEN' \
  -H 'Content-Type: application/json; charset=utf-8' \
  -d '{
        "fields": {
            "任务标题API名": "API 创建的新任务",
            "优先级API名": "高"
        }
      }'</code></pre>
                    </div>
                     <p class="text-sm text-slate-500 mt-1">上面的 cURL 示例中的 `-d` 部分可以被 AI 生成的 `fields` 内容替换。</p>
                </div>

                <div class="space-y-2">
                    <h4 class="text-xl font-medium text-slate-800">响应解析</h4>
                     <p class="text-slate-600">成功创建后，API 返回新记录的完整信息，包括其自动生成的 `record_id`。</p>
                    <div class="code-block">
                        <button class="copy-btn" onclick="copyCode(this)">复制</button>
                        <pre><code>{
    "code": 0,
    "msg": "success",
    "data": {
        "record": {
            "record_id": "recxxxxNEW",
            "fields": {
                "任务标题API名": "API 创建的新任务",
                "优先级API名": "高"
            }
        }
    }
}</code></pre>
                    </div>
                </div>
            </section>

            <section id="api-update-records" class="content-section space-y-6">
                <h2 class="text-3xl font-semibold text-slate-900 border-b pb-3">更新现有记录</h2>
                <p class="text-slate-700">更新记录分为单条更新和批量更新。</p>

                <div class="space-y-4">
                    <h3 class="text-2xl font-medium text-slate-800">单条更新</h3>
                    <div class="space-y-2">
                        <h4 class="text-xl font-medium text-slate-800">API 端点与方法</h4>
                        <p class="text-slate-600"><strong>端点</strong>: <code class="bg-gray-100 px-1 rounded">PUT /open-apis/bitable/v1/apps/:app_token/tables/:table_id/records/:record_id</code></p>
                        <p class="text-slate-600"><strong>路径参数</strong>: `:app_token`, `:table_id`, `:record_id`</p>
                        <p class="text-slate-600"><strong>请求体 (JSON)</strong>: `fields` 对象中仅包含需修改的字段及其新值。</p>
                        <p class="text-slate-600"><strong>所需权限 Scope</strong>: `bitable:app`</p>
                    </div>
                    <div class="space-y-2">
                        <h4 class="text-xl font-medium text-slate-800">cURL 示例</h4>
                        <div class="code-block">
                            <button class="copy-btn" onclick="copyCode(this)">复制</button>
                            <pre><code>curl -X PUT \
  'https://open.feishu.cn/open-apis/bitable/v1/apps/YOUR_APP_TOKEN/tables/YOUR_TABLE_ID/records/YOUR_RECORD_ID' \
  -H 'Authorization: Bearer YOUR_TENANT_ACCESS_TOKEN' \
  -H 'Content-Type: application/json; charset=utf-8' \
  -d '{
        "fields": {
            "任务状态API名": "已完成"
        }
      }'</code></pre>
                        </div>
                    </div>
                </div>

                <div class="space-y-4 mt-6 border-t pt-6">
                    <h3 class="text-2xl font-medium text-slate-800">批量更新</h3>
                    <p class="text-slate-600">对于需要同时更新多条记录的场景，批量更新接口更高效。</p>
                    <div class="space-y-2">
                        <h4 class="text-xl font-medium text-slate-800">API 端点与方法</h4>
                        <p class="text-slate-600"><strong>端点</strong>: <code class="bg-gray-100 px-1 rounded">POST /open-apis/bitable/v1/apps/:app_token/tables/:table_id/records/batch_update</code></p>
                        <p class="text-slate-600"><strong>请求体 (JSON)</strong>: 包含 `records` 数组，每个对象指定 `record_id` 和 `fields`。</p>
                        <div class="code-block">
                            <button class="copy-btn" onclick="copyCode(this)">复制</button>
                            <pre><code>{
    "records": [
        {
            "record_id": "recxxxx1",
            "fields": { "任务状态API名": "进行中" }
        },
        {
            "record_id": "recxxxx2",
            "fields": { "优先级API名": "低" }
        }
    ]
}</code></pre>
                        </div>
                        <p class="text-slate-600"><strong>所需权限 Scope</strong>: `bitable:app`</p>
                    </div>
                </div>
            </section>

            <section id="api-delete-records" class="content-section space-y-6">
                <h2 class="text-3xl font-semibold text-slate-900 border-b pb-3">删除记录</h2>
                <p class="text-slate-700">从数据表中删除指定的记录。</p>

                <div class="space-y-2">
                    <h4 class="text-xl font-medium text-slate-800">API 端点与方法</h4>
                    <p class="text-slate-600"><strong>端点</strong>: <code class="bg-gray-100 px-1 rounded">DELETE /open-apis/bitable/v1/apps/:app_token/tables/:table_id/records/:record_id</code></p>
                    <p class="text-slate-600"><strong>路径参数</strong>: `:app_token`, `:table_id`, `:record_id`</p>
                    <p class="text-slate-600"><strong>所需权限 Scope</strong>: `bitable:app`</p>
                </div>

                <div class="space-y-2">
                    <h4 class="text-xl font-medium text-slate-800">cURL 示例</h4>
                    <div class="code-block">
                        <button class="copy-btn" onclick="copyCode(this)">复制</button>
                        <pre><code>curl -X DELETE \
  'https://open.feishu.cn/open-apis/bitable/v1/apps/YOUR_APP_TOKEN/tables/YOUR_TABLE_ID/records/YOUR_RECORD_ID' \
  -H 'Authorization: Bearer YOUR_TENANT_ACCESS_TOKEN' \
  -H 'Content-Type: application/json; charset=utf-8'</code></pre>
                    </div>
                </div>

                <div class="space-y-2">
                    <h4 class="text-xl font-medium text-slate-800">响应解析</h4>
                     <p class="text-slate-600">成功删除后，API 返回确认信息。</p>
                    <div class="code-block">
                        <button class="copy-btn" onclick="copyCode(this)">复制</button>
                        <pre><code>{
    "code": 0,
    "msg": "success",
    "data": {
        "deleted": true,
        "record_id": "YOUR_RECORD_ID"
    }
}</code></pre>
                    </div>
                </div>
                <p class="text-slate-600">报告中提到可能存在批量删除接口，建议查阅官方文档确认。</p>
            </section>

            <section id="sdk-usage" class="content-section space-y-6">
                <h2 class="text-3xl font-semibold text-slate-900 border-b pb-3">使用 SDK (推荐)</h2>
                <p class="text-slate-700">
                    直接使用 HTTP 请求调用 API 虽然可行，但处理认证、请求构建、响应解析等较为繁琐。使用官方或社区 SDK 可以大大简化开发工作。SDK 通常会封装底层通信，提供更友好的编程接口，并自动管理 `access_token`。
                </p>
                <p class="text-slate-600">
                    飞书官方提供了多种语言的 SDK，如 Python (`lark-oapi`)、Java (`oapi-sdk-java`) 和 Node.js (`lark-openapi-mcp`) 等。
                </p>

                <div class="space-y-4">
                    <h3 class="text-2xl font-medium text-slate-800">Python SDK 示例 (`lark-oapi`) - 搜索记录</h3>
                    <p class="text-slate-600">以下示例演示如何使用飞书官方 Python SDK (`lark-oapi`) 来搜索多维表格中的记录。此 SDK 会自动处理 `tenant_access_token` 的获取和刷新。</p>
                    
                    <div class="space-y-2">
                        <h4 class="text-lg font-medium text-slate-800">安装 SDK:</h4>
                        <div class="code-block">
                            <button class="copy-btn" onclick="copyCode(this)">复制</button>
                            <pre><code>pip install lark-oapi</code></pre>
                        </div>
                    </div>

                    <div class="space-y-2">
                        <h4 class="text-lg font-medium text-slate-800">Python 代码示例:</h4>
                        <div class="code-block">
                            <button class="copy-btn" onclick="copyCode(this)">复制</button>
                            <pre><code class="language-python">import lark_oapi as lark
from lark_oapi.api.bitable.v1 import *

# 1. 配置应用凭证和多维表格信息
APP_ID = "YOUR_APP_ID"
APP_SECRET = "YOUR_APP_SECRET"
BITABLE_APP_TOKEN = "YOUR_APP_TOKEN"
TABLE_ID = "YOUR_TABLE_ID"

# 2. 构建 HTTP 客户端 (SDK 会自动处理 tenant_access_token)
client = lark.Client.builder() \
   .app_id(APP_ID) \
   .app_secret(APP_SECRET) \
   .log_level(lark.LogLevel.DEBUG) \
   .build()

# 3. 构建搜索记录请求
# 假设搜索“任务标题”字段包含“示例”的记录
filter_condition = Filter.builder() \
   .conjunction("and") \
   .conditions([
        Condition.builder() \
           .field_name("任务标题") \
           .operator("contains") \
           .value(["示例"]) \
           .build()
    ]) \
   .build()

req_body = SearchAppTableRecordRequestBody.builder() \
   .table_id(TABLE_ID) \
   .field_names(["任务标题", "截止日期"]) \
   .filter(filter_condition) \
   .page_size(10) \
   .build()

request = SearchAppTableRecordRequest.builder() \
   .app_token(BITABLE_APP_TOKEN) \
   .table_id(TABLE_ID) \
   .request_body(req_body) \
   .build()

# 4. 发起请求并处理响应
try:
    response: SearchAppTableRecordResponse = client.bitable.v1.app_table_record.search(request)

    if response.success():
        print("API 调用成功:")
        if response.data.items:
            for item in response.data.items:
                record_fields = {}
                if item.fields:
                    for field_name, field_value in item.fields.items():
                        record_fields[field_name] = field_value
                print(f"  Record ID: {item.record_id}, Fields: {record_fields}")
        else:
            print("  未找到满足条件的记录。")
        if response.data.has_more:
            print(f"  还有更多记录，下一页的 page_token: {response.data.page_token}")
        print(f"  总记录数: {response.data.total if response.data.total is not None else '未知'}")
    else:
        print(f"API 调用失败，错误码: {response.code}, 错误信息: {response.msg}")
        print(f"  Log ID: {response.get_log_id()}")
except Exception as e:
    print(f"执行过程中发生异常: {e}")
</code></pre>
                        </div>
                        <p class="text-sm text-slate-500 mt-1">请将示例中的 `YOUR_APP_ID`, `YOUR_APP_SECRET`, `YOUR_APP_TOKEN`, `YOUR_TABLE_ID` 以及字段API名替换为您的实际值。</p>
                    </div>
                </div>
            </section>

            <section id="important-notes" class="content-section space-y-6">
                <h2 class="text-3xl font-semibold text-slate-900 border-b pb-3">重要注意事项</h2>
                <p class="text-slate-700">在使用飞书多维表格 API 时，有几个关键点需要特别注意，以确保应用的稳定性和正确性。点击下方各项展开查看详情：</p>
                
                <div class="space-y-3">
                    <div class="accordion-item border border-gray-200 rounded-md">
                        <div class="accordion-header bg-gray-50 p-4 flex justify-between items-center">
                            <h4 class="text-xl font-medium text-slate-800">A. API 调用频率限制</h4>
                            <span class="accordion-icon text-xl text-slate-500 transform transition-transform duration-300">▼</span>
                        </div>
                        <div class="accordion-content p-4 border-t border-gray-200 text-slate-600 space-y-3">
                            <p>飞书开放平台对 API 调用设有频率限制。超出限制将返回错误。不同端点限制可能不同，例如：</p>
                            <div class="chart-container my-4">
                                <canvas id="rateLimitChart"></canvas>
                            </div>
                            <p class="text-sm text-center text-slate-500">图表：部分API每秒调用次数限制示例</p>
                            <p><strong>应对策略</strong>:</p>
                            <ul class="list-disc list-inside pl-4 space-y-1">
                                <li><strong>批处理</strong>: 尽可能使用批量操作接口。</li>
                                <li><strong>缓存</strong>: 缓存不经常变动的数据。</li>
                                <li><strong>合理重试</strong>: 采用带指数退避的重试策略。</li>
                                <li><strong>优化查询</strong>: 精心设计查询条件。</li>
                            </ul>
                        </div>
                    </div>

                    <div class="accordion-item border border-gray-200 rounded-md">
                        <div class="accordion-header bg-gray-50 p-4 flex justify-between items-center">
                            <h4 class="text-xl font-medium text-slate-800">B. 错误处理机制</h4>
                             <span class="accordion-icon text-xl text-slate-500 transform transition-transform duration-300">▼</span>
                        </div>
                        <div class="accordion-content p-4 border-t border-gray-200 text-slate-600 space-y-3">
                            <p>API 调用并非总能成功。健全的错误处理机制必不可少。</p>
                            <ul class="list-disc list-inside pl-4 space-y-1">
                                <li><strong>响应结构</strong>: 检查响应体中的 `code` (0 表示成功) 和 `msg`。</li>
                                <li><strong>常见错误码</strong>: 如权限问题 (`1254302`)、参数错误 (`1254000`)、资源未找到 (`1254040`)、频率超限 (`1254290`)、写冲突 (`1254291`) 等。详细列表见各 API 文档。</li>
                                <li><strong>日志记录</strong>: 记录请求、响应信息及 `log_id` (通常是 `X-Tt-Logid` 响应头或 SDK 封装的 `response.get_log_id()`)，便于排查问题。</li>
                            </ul>
                        </div>
                    </div>

                    <div class="accordion-item border border-gray-200 rounded-md">
                        <div class="accordion-header bg-gray-50 p-4 flex justify-between items-center">
                            <h4 class="text-xl font-medium text-slate-800">C. 字段 API 名称 vs. 显示名称</h4>
                             <span class="accordion-icon text-xl text-slate-500 transform transition-transform duration-300">▼</span>
                        </div>
                        <div class="accordion-content p-4 border-t border-gray-200 text-slate-600 space-y-3">
                            <p><strong>关键</strong>: API 调用时（如 `filter` 条件、添加/更新记录的 `fields` 对象），<strong>必须使用字段的 API 名称 (Field API Name)</strong>，而非界面上看到的“显示名称”。</p>
                            <ul class="list-disc list-inside pl-4 space-y-1">
                                <li><strong>API 名称</strong>: 创建时设定，稳定不变，用于程序化访问。</li>
                                <li><strong>显示名称</strong>: 用户可修改，不适合 API。</li>
                                <li><strong>获取 API 名称</strong>: 可通过“列出字段”元数据 API 查询。</li>
                            </ul>
                            <p>不使用正确的 API 字段名会导致错误，如 `FieldNameNotFound`。</p>
                        </div>
                    </div>
                    
                    <div class="accordion-item border border-gray-200 rounded-md">
                        <div class="accordion-header bg-gray-50 p-4 flex justify-between items-center">
                            <h4 class="text-xl font-medium text-slate-800">D. 数据一致性与并发</h4>
                             <span class="accordion-icon text-xl text-slate-500 transform transition-transform duration-300">▼</span>
                        </div>
                        <div class="accordion-content p-4 border-t border-gray-200 text-slate-600 space-y-3">
                            <p>多维表格是协作工具，可能有多人或多应用同时操作。</p>
                            <ul class="list-disc list-inside pl-4 space-y-1">
                                <li><strong>写操作建议</strong>: 官方建议单个 Base (app_token) 一次最好只执行一个 API 写操作。</li>
                                <li><strong>并发写冲突</strong>: 可能遇到错误码 `1254291`。应用层面需设计队列或加锁机制。</li>
                                <li><strong>一致性检查参数</strong>: 如 `ignore_consistency_check` (默认为 `false`)，审慎使用。</li>
                            </ul>
                        </div>
                    </div>

                    <div class="accordion-item border border-gray-200 rounded-md">
                        <div class="accordion-header bg-gray-50 p-4 flex justify-between items-center">
                            <h4 class="text-xl font-medium text-slate-800">E. 权限细化与高级权限</h4>
                             <span class="accordion-icon text-xl text-slate-500 transform transition-transform duration-300">▼</span>
                        </div>
                        <div class="accordion-content p-4 border-t border-gray-200 text-slate-600 space-y-3">
                            <p>API 访问多维表格的权限是双重体系：</p>
                            <ol class="list-decimal list-inside pl-4 space-y-1">
                                <li><strong>应用级 API Scope</strong>: 在开放平台为应用申请的权限 (如 `bitable:app`)。</li>
                                <li><strong>文档级权限</strong>:
                                    <ul class="list-disc list-inside pl-6">
                                        <li><strong>协作者权限</strong>: 应用必须被添加为目标多维表格的协作者，并授予相应读写权限。</li>
                                        <li><strong>高级权限</strong>: 若多维表格开启了“高级权限”，需在其中为应用或用户配置相应权限。</li>
                                    </ul>
                                </li>
                            </ol>
                            <p>权限错误 (如 `1254302: Permission denied`) 时，需同时检查这两方面配置。</p>
                        </div>
                    </div>
                </div>
            </section>

            <section id="resources" class="content-section space-y-6">
                <h2 class="text-3xl font-semibold text-slate-900 border-b pb-3">总结与后续资源</h2>
                <p class="text-slate-700">
                    通过飞书开放平台提供的 API，开发者可以高效地访问和操作多维表格数据。核心步骤包括创建应用、获取凭证和访问令牌，然后调用相应 API 端点。使用 SDK 能简化开发。
                </p>
                <p class="text-slate-600">
                    在开发过程中，务必关注 API 频率限制、错误处理、字段命名、数据一致性与并发，以及权限配置。新增的 ✨ AI 智能辅助功能可以帮助您更便捷地构造请求。
                </p>
                <p class="text-slate-600">
                    为了获取最全面、最新的 API 信息、SDK 指南和最佳实践，强烈建议您查阅以下官方资源：
                </p>
                <ul class="list-disc list-inside space-y-2 text-blue-600 pl-4">
                    <li><a href="https://open.feishu.cn/document" target="_blank" class="hover:underline">飞书开放平台官方文档</a></li>
                    <li><a href="https://open.feishu.cn/community" target="_blank" class="hover:underline">飞书开发者社区</a></li>
                </ul>
            </section>
        </main>
    </div>

    <footer class="bg-slate-100 border-t border-slate-200 mt-12">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-6 text-center text-sm text-slate-500">
            <p>&copy; <span id="currentYear"></span> 飞书多维表格 API 交互式指南。基于报告内容生成，并集成Gemini AI功能。</p>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const navLinks = document.querySelectorAll('.nav-link');
            const contentSections = document.querySelectorAll('.content-section');
            const accordionItems = document.querySelectorAll('.accordion-item');

            function showSection(targetId) {
                contentSections.forEach(section => {
                    section.classList.remove('active');
                });
                navLinks.forEach(link => {
                    link.classList.remove('active');
                });

                const targetSection = document.getElementById(targetId);
                if (targetSection) {
                    targetSection.classList.add('active');
                }
                const activeLink = document.querySelector(`.nav-link[data-target="${targetId}"]`);
                if (activeLink) {
                    activeLink.classList.add('active');
                }
                 window.scrollTo(0, 0);
            }

            navLinks.forEach(link => {
                link.addEventListener('click', function (e) {
                    e.preventDefault();
                    const targetId = this.dataset.target;
                    showSection(targetId);
                });
            });
            
            accordionItems.forEach(item => {
                const header = item.querySelector('.accordion-header');
                const content = item.querySelector('.accordion-content');
                const icon = item.querySelector('.accordion-icon');

                header.addEventListener('click', () => {
                    const isOpen = content.classList.contains('open');
                    content.classList.toggle('open');
                    icon.style.transform = content.classList.contains('open') ? 'rotate(180deg)' : 'rotate(0deg)';
                });
            });

            const initialTarget = 'home';
            showSection(initialTarget);
            if (document.getElementById(initialTarget)) {
                 document.querySelector(`.nav-link[data-target="${initialTarget}"]`).classList.add('active');
            }

            const rateLimitChartCtx = document.getElementById('rateLimitChart');
            if (rateLimitChartCtx) {
                new Chart(rateLimitChartCtx, {
                    type: 'bar',
                    data: {
                        labels: ['列出数据表', '删除单条记录', '批量获取记录', '批量更新记录', '搜索记录'],
                        datasets: [{
                            label: '每秒调用次数限制',
                            data: [20, 10, 20, 50, 20],
                            backgroundColor: [
                                'rgba(59, 130, 246, 0.7)', 
                                'rgba(239, 68, 68, 0.7)',  
                                'rgba(245, 158, 11, 0.7)', 
                                'rgba(16, 185, 129, 0.7)', 
                                'rgba(139, 92, 246, 0.7)'  
                            ],
                            borderColor: [
                                'rgba(59, 130, 246, 1)',
                                'rgba(239, 68, 68, 1)',
                                'rgba(245, 158, 11, 1)',
                                'rgba(16, 185, 129, 1)',
                                'rgba(139, 92, 246, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: { beginAtZero: true, title: { display: true, text: '请求数 / 秒'}},
                            x: { title: {display: true, text: 'API 操作'}}
                        },
                        plugins: {
                            legend: { display: false },
                            tooltip: { callbacks: { label: function(context) { return `${context.dataset.label}: ${context.parsed.y} 次/秒`;}}}
                        }
                    }
                });
            }
            
            document.getElementById('currentYear').textContent = new Date().getFullYear();

            // Gemini API Integration
            const apiKey = ""; // API Key will be provided by the Canvas environment

            const generateRecordBtn = document.getElementById('generateRecordBtn');
            const aiRecordDescriptionInput = document.getElementById('aiRecordDescriptionInput');
            const aiRecordStatus = document.getElementById('aiRecordStatus');
            const addRecordRequestExamplePre = document.querySelector('#addRecordRequestExample pre code');

            if (generateRecordBtn) {
                generateRecordBtn.addEventListener('click', async () => {
                    const userDescription = aiRecordDescriptionInput.value.trim();
                    if (!userDescription) {
                        aiRecordStatus.textContent = '请输入记录描述。';
                        aiRecordStatus.className = 'gemini-status text-red-600';
                        return;
                    }

                    aiRecordStatus.textContent = 'AI 正在生成记录内容...';
                    aiRecordStatus.className = 'gemini-status text-blue-600';
                    generateRecordBtn.disabled = true;

                    const prompt = `你是一个飞书多维表格API的助手。用户想要添加一条新记录。根据用户的简短描述：“${userDescription}”，请为这条记录生成一个JSON对象，该对象将用于填充飞书API请求体中的 'fields' 部分。请只返回 'fields' 对象的JSON内容，例如：{\"任务标题API名\": \"示例任务\", \"优先级API名\": \"中\", \"截止日期API名\": \"2025-12-31\"}。请确保字段名使用中文并以 'API名' 结尾，并尝试生成一些常见的任务相关字段，如任务内容、优先级、截止日期、状态等。`;

                    try {
                        const payload = { contents: [{ role: "user", parts: [{ text: prompt }] }] };
                        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
                        
                        const response = await fetch(apiUrl, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(payload)
                        });

                        if (!response.ok) {
                            throw new Error(`API 请求失败，状态码: ${response.status}`);
                        }

                        const result = await response.json();
                        
                        if (result.candidates && result.candidates.length > 0 &&
                            result.candidates[0].content && result.candidates[0].content.parts &&
                            result.candidates[0].content.parts.length > 0) {
                            
                            let generatedJsonText = result.candidates[0].content.parts[0].text;
                            // Sanitize the output to ensure it's valid JSON for the 'fields' object
                            generatedJsonText = generatedJsonText.replace(/^```json\s*|```\s*$/g, '').trim();
                            
                            try {
                                // Validate if it's a valid JSON object
                                JSON.parse(generatedJsonText); 
                                const currentRequestExample = JSON.parse(addRecordRequestExamplePre.textContent);
                                currentRequestExample.fields = JSON.parse(generatedJsonText); // Replace fields
                                addRecordRequestExamplePre.textContent = JSON.stringify(currentRequestExample, null, 4);
                                aiRecordStatus.textContent = 'AI 生成成功！已填充到下方请求体示例。';
                                aiRecordStatus.className = 'gemini-status text-green-600';
                            } catch (e) {
                                 aiRecordStatus.textContent = 'AI 生成的内容不是有效的JSON对象。请重试或调整描述。';
                                 aiRecordStatus.className = 'gemini-status text-red-600';
                                 console.error("Generated text is not valid JSON for fields:", generatedJsonText, e);
                            }
                        } else {
                            throw new Error('AI 未能生成有效内容。');
                        }
                    } catch (error) {
                        aiRecordStatus.textContent = `AI 生成失败: ${error.message}`;
                        aiRecordStatus.className = 'gemini-status text-red-600';
                        console.error('Gemini API error (Record Generation):', error);
                    } finally {
                        generateRecordBtn.disabled = false;
                    }
                });
            }


            const generateFilterBtn = document.getElementById('generateFilterBtn');
            const aiFilterQueryInput = document.getElementById('aiFilterQueryInput');
            const aiFilterStatus = document.getElementById('aiFilterStatus');
            const searchRecordsCurlExamplePre = document.querySelector('#searchRecordsCurlExample pre code');

            if (generateFilterBtn) {
                generateFilterBtn.addEventListener('click', async () => {
                    const userNaturalQuery = aiFilterQueryInput.value.trim();
                    if (!userNaturalQuery) {
                        aiFilterStatus.textContent = '请输入自然语言筛选查询。';
                        aiFilterStatus.className = 'gemini-status text-red-600';
                        return;
                    }

                    aiFilterStatus.textContent = 'AI 正在生成筛选条件...';
                    aiFilterStatus.className = 'gemini-status text-blue-600';
                    generateFilterBtn.disabled = true;

                    const prompt = `你是一个飞书多维表格API的助手，特别擅长构建筛选查询。用户想要通过自然语言描述来生成一个用于飞书多维表格“搜索记录”API的 'filter' JSON对象。用户的描述是：“${userNaturalQuery}”。请根据这个描述，生成一个符合飞书API规范的 'filter' JSON对象。例如，如果用户说“查找状态为打开且优先级为高的任务”，你应该生成类似：{\"conjunction\": \"and\", \"conditions\": [{\"field_name\": \"状态API名\", \"operator\": \"is\", \"value\": [\"打开\"]}, {\"field_name\": \"优先级API名\", \"operator\": \"is\", \"value\": [\"高\"]}]}。请确保字段名使用中文并以 'API名' 结尾，并根据用户描述智能推断字段名、操作符和值。只返回 'filter' 对象的JSON内容。`;
                    
                    try {
                        const payload = { contents: [{ role: "user", parts: [{ text: prompt }] }] };
                        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
                        
                        const response = await fetch(apiUrl, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(payload)
                        });

                        if (!response.ok) {
                            throw new Error(`API 请求失败，状态码: ${response.status}`);
                        }
                        const result = await response.json();

                        if (result.candidates && result.candidates.length > 0 &&
                            result.candidates[0].content && result.candidates[0].content.parts &&
                            result.candidates[0].content.parts.length > 0) {
                            
                            let generatedJsonText = result.candidates[0].content.parts[0].text;
                            generatedJsonText = generatedJsonText.replace(/^```json\s*|```\s*$/g, '').trim();

                            try {
                                const generatedFilterObject = JSON.parse(generatedJsonText);
                                // Update the cURL example
                                const currentCurlText = searchRecordsCurlExamplePre.textContent;
                                const curlParts = currentCurlText.split("-d '");
                                if (curlParts.length === 2) {
                                    let requestBodyJson;
                                    try {
                                        // Extract the existing JSON part and parse it
                                        const existingJsonString = curlParts[1].slice(0, -1); // Remove trailing '
                                        requestBodyJson = JSON.parse(existingJsonString);
                                    } catch (parseError) {
                                        // If parsing fails, fallback to a default structure
                                        console.warn("Could not parse existing cURL body, using default structure.", parseError);
                                        requestBodyJson = {
                                            filter: {},
                                            page_size: 10,
                                            field_names: ["任务标题API名", "负责人API名", "状态字段API名"]
                                        };
                                    }
                                    requestBodyJson.filter = generatedFilterObject; // Replace/add filter
                                    const newCurlBody = JSON.stringify(requestBodyJson, null, 4);
                                    // Indent lines after the first for readability in <pre>
                                    const indentedNewCurlBody = newCurlBody.split('\n').map((line, index) => index > 0 ? '        ' + line : line).join('\n');
                                    searchRecordsCurlExamplePre.textContent = `${curlParts[0]}-d '{\n        ${indentedNewCurlBody}\n      }'`;

                                } else {
                                     searchRecordsCurlExamplePre.textContent = searchRecordsCurlExamplePre.textContent.replace(/"filter":\s*{[^}]*}/, `"filter": ${JSON.stringify(generatedFilterObject, null, 4)}` )
                                }
                                aiFilterStatus.textContent = 'AI 生成成功！已更新下方cURL示例中的 filter 部分。';
                                aiFilterStatus.className = 'gemini-status text-green-600';

                            } catch (e) {
                                aiFilterStatus.textContent = 'AI 生成的内容不是有效的JSON filter 对象。请重试或调整描述。';
                                aiFilterStatus.className = 'gemini-status text-red-600';
                                console.error("Generated text is not valid JSON for filter:", generatedJsonText, e);
                            }
                        } else {
                            throw new Error('AI 未能生成有效内容。');
                        }
                    } catch (error) {
                        aiFilterStatus.textContent = `AI 生成失败: ${error.message}`;
                        aiFilterStatus.className = 'gemini-status text-red-600';
                        console.error('Gemini API error (Filter Generation):', error);
                    } finally {
                        generateFilterBtn.disabled = false;
                    }
                });
            }

        });

        function copyCode(buttonElement) {
            const preElement = buttonElement.nextElementSibling;
            const codeToCopy = preElement.innerText;
            
            const tempTextArea = document.createElement('textarea');
            tempTextArea.value = codeToCopy;
            document.body.appendChild(tempTextArea);
            tempTextArea.select();
            document.execCommand('copy');
            document.body.removeChild(tempTextArea);

            const originalText = buttonElement.innerText;
            buttonElement.innerText = '已复制!';
            setTimeout(() => {
                buttonElement.innerText = originalText;
            }, 1500);
        }
    </script>
</body>
</html>
